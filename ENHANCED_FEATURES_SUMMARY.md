# Enhanced Order Groups Features Summary

## Overview
The TabOrders functionality has been enhanced with configurable order groups and separate toggle controls for Stop Loss (SL) and Take Profit (TP) management.

## New Features

### 1. Configurable Order Groups (config.py)
- Added `order_groups` dictionary in config.py for easy group management
- Each group has:
  - `prefix`: Comment prefix to filter orders (e.g., "ZD", "IN", "WH", "" for All)
  - `display_name`: Human-readable name for UI
  - `has_group_id`: Whether the group uses group IDs for organization
  - `default_sl_enabled`: Default state for SL toggle
  - `default_tp_enabled`: Default state for TP toggle

### 2. WH Orders Support
- Added new "WH Orders" subtab
- Shows all orders starting with "WH" prefix
- No group ID filtering (shows all WH orders individually)
- Includes close functionality for individual WH orders

### 3. Dynamic Toggle Controls
- **Auto SL to BE**: Dynamically generated toggles for each group (All, ZD, IN, WH)
- **Auto Moving TP**: Separate toggles for moving TP functionality
- Both use the same group configuration for consistency

### 4. Enhanced Processing Logic
- Groups with both SL and TP enabled: Moves both SL and TP together
- Groups with only SL enabled: Moves only SL to breakeven
- Groups with only TP enabled: Moves TP (and SL as side effect)
- Prevents duplicate processing of the same group

## Configuration Structure

```python
self.order_groups = {
    "ALL": {
        "prefix": "",
        "display_name": "All",
        "has_group_id": False,
        "default_sl_enabled": True,
        "default_tp_enabled": False
    },
    "ZD": {
        "prefix": "ZD",
        "display_name": "ZD", 
        "has_group_id": True,
        "default_sl_enabled": False,
        "default_tp_enabled": False
    },
    "IN": {
        "prefix": "IN",
        "display_name": "IN",
        "has_group_id": True, 
        "default_sl_enabled": False,
        "default_tp_enabled": False
    },
    "WH": {
        "prefix": "WH",
        "display_name": "WH",
        "has_group_id": False,
        "default_sl_enabled": False,
        "default_tp_enabled": False
    }
}
```

## How to Add New Groups

1. **Add to config.py**:
   ```python
   "NEW_GROUP": {
       "prefix": "NG",
       "display_name": "New Group",
       "has_group_id": True,  # or False
       "default_sl_enabled": False,
       "default_tp_enabled": False
   }
   ```

2. **Add subtab (if needed)**:
   - Follow the pattern of existing subtabs in `tab_orders.py`
   - Add refresh method and close functionality

3. **No code changes needed**:
   - Toggle switches are generated automatically
   - Processing logic uses the configuration dynamically

## UI Layout

### Control Panel (3 rows):
1. **Auto Refresh**: Enable/disable, interval setting, manual refresh
2. **Auto SL to BE**: Toggle switches for All, ZD, IN, WH groups
3. **Auto Moving TP**: Toggle switches for All, ZD, IN, WH groups

### Subtabs:
1. **All Orders**: Combined view of positions and pending orders
2. **ZD Orders**: Grouped view by ZD ID
3. **IN Orders**: Grouped view by IN ID  
4. **WH Orders**: Individual list view (no grouping)

## Status Display
- Shows current state of refresh, loop, Auto BE, and Auto TP
- Color-coded indicators for active/inactive states
- Debug function shows detailed status of all toggles

## Benefits
1. **Easy Configuration**: Add/modify groups in config.py only
2. **Flexible Control**: Separate SL and TP toggles for each group
3. **WH Support**: Full support for WH orders without group ID complexity
4. **Maintainable**: Dynamic generation reduces code duplication
5. **Extensible**: Easy to add new order types/groups in the future
