2025-07-11 21:29:07,679 - INFO - log_toggle_action:56 - DEBUG: Toggle action - Type: Auto SL to BE, Group: ZD, Status: True
2025-07-11 21:29:07,680 - INFO - log_toggle_action:56 - DEBUG: Toggle action - Type: Auto Moving TP, Group: WH, Status: False
2025-07-11 21:29:07,683 - INFO - log_order_processing:75 - DEBUG: Order processing - Symbol: XAUUSD, Group: ZD, Total: 5, Processed: 3, Action: SL+TP
2025-07-11 21:29:07,683 - INFO - log_order_processing:75 - DEBUG: Order processing - Symbol: XAUUSD, Group: , Total: 10, Processed: 7, Action: SL to BE
2025-07-11 21:29:07,684 - INFO - log_order_action:97 - DEBUG: Order action - Ticket: 12345, Action: SL to BE, SL: 1950.5 -> 1951.0, Success: True
2025-07-11 21:29:07,688 - INFO - log_order_action:97 - DEBUG: Order action - Ticket: 12346, Action: Moving TP, TP: 1952.0 -> 1953.0, Success: True
2025-07-11 21:29:07,689 - DEBUG - log_magic_number_info:110 - DEBUG: Magic number usage - Ticket: 12345, Magic: 1000, SL Points: 1000, Distance: 10.00000, Symbol: XAUUSD
2025-07-11 21:29:07,691 - ERROR - log_error:125 - #12345 - ERROR in test context: Test error
NoneType: None
2025-07-11 21:29:07,738 - INFO - log_toggle_action:56 - DEBUG: Toggle action - Type: Test Action, Group: Test Group, Status: True
2025-07-11 21:29:07,738 - DEBUG - debug_print:154 - This should be visible in debug mode
2025-07-11 21:29:07,748 - INFO - log_toggle_action:64 - Toggle Test Action Test Group: disabled
2025-07-11 21:29:40,208 - INFO - log_toggle_action:56 - DEBUG: Toggle action - Type: Auto SL to BE, Group: ZD, Status: True
2025-07-11 21:29:40,210 - INFO - log_toggle_action:56 - DEBUG: Toggle action - Type: Auto Moving TP, Group: WH, Status: False
2025-07-11 21:29:40,210 - INFO - log_order_processing:75 - DEBUG: Order processing - Symbol: XAUUSD, Group: ZD, Total: 5, Processed: 3, Action: SL+TP
2025-07-11 21:29:40,210 - INFO - log_order_processing:75 - DEBUG: Order processing - Symbol: XAUUSD, Group: , Total: 10, Processed: 7, Action: SL to BE
2025-07-11 21:29:40,210 - INFO - log_order_action:97 - DEBUG: Order action - Ticket: 12345, Action: SL to BE, SL: 1950.5 -> 1951.0, Success: True
2025-07-11 21:29:40,210 - INFO - log_order_action:97 - DEBUG: Order action - Ticket: 12346, Action: Moving TP, TP: 1952.0 -> 1953.0, Success: True
2025-07-11 21:29:40,221 - DEBUG - log_magic_number_info:110 - DEBUG: Magic number usage - Ticket: 12345, Magic: 1000, SL Points: 1000, Distance: 10.00000, Symbol: XAUUSD
2025-07-11 21:29:40,222 - ERROR - log_error:125 - #12345 - ERROR in test context: Test error
NoneType: None
2025-07-11 21:29:40,246 - INFO - log_toggle_action:56 - DEBUG: Toggle action - Type: Test Action, Group: Test Group, Status: True
2025-07-11 21:29:40,246 - DEBUG - debug_print:154 - This should be visible in debug mode
2025-07-11 21:29:40,253 - INFO - log_toggle_action:64 - Toggle Test Action Test Group: disabled
2025-07-11 21:30:26,403 - INFO - log_toggle_action:56 - DEBUG: Toggle action - Type: Auto SL to BE, Group: ZD, Status: True
2025-07-11 21:30:26,403 - INFO - log_toggle_action:56 - DEBUG: Toggle action - Type: Auto Moving TP, Group: WH, Status: False
2025-07-11 21:30:26,403 - INFO - log_order_processing:75 - DEBUG: Order processing - Symbol: XAUUSD, Group: ZD, Total: 5, Processed: 3, Action: SL+TP
2025-07-11 21:30:26,403 - INFO - log_order_processing:75 - DEBUG: Order processing - Symbol: XAUUSD, Group: , Total: 10, Processed: 7, Action: SL to BE
2025-07-11 21:30:26,403 - INFO - log_order_action:97 - DEBUG: Order action - Ticket: 12345, Action: SL to BE, SL: 1950.5 -> 1951.0, Success: True
2025-07-11 21:30:26,403 - INFO - log_order_action:97 - DEBUG: Order action - Ticket: 12346, Action: Moving TP, TP: 1952.0 -> 1953.0, Success: True
2025-07-11 21:30:26,411 - DEBUG - log_magic_number_info:110 - DEBUG: Magic number usage - Ticket: 12345, Magic: 1000, SL Points: 1000, Distance: 10.00000, Symbol: XAUUSD
2025-07-11 21:30:26,411 - ERROR - log_error:125 - #12345 - ERROR in test context: Test error
NoneType: None
2025-07-11 21:30:26,452 - INFO - log_toggle_action:56 - DEBUG: Toggle action - Type: Test Action, Group: Test Group, Status: True
2025-07-11 21:30:26,452 - DEBUG - debug_print:154 - This should be visible in debug mode
2025-07-11 21:30:26,466 - INFO - log_toggle_action:64 - Toggle Test Action Test Group: disabled
2025-07-11 21:31:02,023 - INFO - log_toggle_action:56 - DEBUG: Toggle action - Type: Auto SL to BE, Group: ZD, Status: True
2025-07-11 21:31:02,027 - INFO - log_toggle_action:56 - DEBUG: Toggle action - Type: Auto Moving TP, Group: WH, Status: False
2025-07-11 21:31:02,030 - INFO - log_order_processing:75 - DEBUG: Order processing - Symbol: XAUUSD, Group: ZD, Total: 5, Processed: 3, Action: SL+TP
2025-07-11 21:31:02,030 - INFO - log_order_processing:75 - DEBUG: Order processing - Symbol: XAUUSD, Group: , Total: 10, Processed: 7, Action: SL to BE
2025-07-11 21:31:02,030 - INFO - log_order_action:97 - DEBUG: Order action - Ticket: 12345, Action: SL to BE, SL: 1950.5 -> 1951.0, Success: True
2025-07-11 21:31:02,030 - INFO - log_order_action:97 - DEBUG: Order action - Ticket: 12346, Action: Moving TP, TP: 1952.0 -> 1953.0, Success: True
2025-07-11 21:31:02,030 - DEBUG - log_magic_number_info:110 - DEBUG: Magic number usage - Ticket: 12345, Magic: 1000, SL Points: 1000, Distance: 10.00000, Symbol: XAUUSD
2025-07-11 21:31:02,035 - ERROR - log_error:125 - #12345 - ERROR in test context: Test error
NoneType: None
2025-07-11 21:31:02,056 - INFO - log_toggle_action:56 - DEBUG: Toggle action - Type: Test Action, Group: Test Group, Status: True
2025-07-11 21:31:02,059 - DEBUG - debug_print:154 - This should be visible in debug mode
2025-07-11 21:31:02,066 - INFO - log_toggle_action:64 - Toggle Test Action Test Group: disabled
2025-07-11 21:38:52,990 - INFO - log_info:142 - 🟢 Orders processing loop started
