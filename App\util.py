import MetaTrader5 as mt5
import customtkinter as ctk
import pandas as pd
import talib as ta
import requests
import time
from datetime import datetime, time as dtime
import logging
import os
import gc
import threading
from functools import wraps
import signal
from App.debug_logger import DebugLogger
# ===========================
# Timeout and Safety Decorators
# ===========================
def timeout_handler(signum, frame):
    raise TimeoutError("Operation timed out")

def with_timeout(seconds=5):
    """Decorator to add timeout to functions"""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            try:
                # Set timeout signal (only works on Unix-like systems)
                if hasattr(signal, 'SIGALRM'):
                    old_handler = signal.signal(signal.SIGALRM, timeout_handler)
                    signal.alarm(seconds)

                result = func(*args, **kwargs)

                if hasattr(signal, 'SIGALRM'):
                    signal.alarm(0)  # Cancel alarm
                    signal.signal(signal.SIGALRM, old_handler)

                return result
            except TimeoutError:
                print(f"Function {func.__name__} timed out after {seconds} seconds")
                return None
            except Exception as e:
                print(f"Error in {func.__name__}: {e}")
                return None
        return wrapper
    return decorator

def safe_mt5_operation(func):
    """Decorator to safely handle MT5 operations"""
    @wraps(func)
    def wrapper(*args, **kwargs):
        try:
            # Check MT5 connection first
            if not mt5.initialize():
                print(f"MT5 not initialized for {func.__name__}")
                return None

            result = func(*args, **kwargs)
            return result
        except Exception as e:
            print(f"MT5 operation error in {func.__name__}: {e}")
            return None
    return wrapper

# ===========================
# Class: Util
# ===========================
class Util:
    def __init__(self, config):
        self.config = config
        self._mt5_lock = threading.Lock()  # Thread safety for MT5 operations

        # Initialize debug logger
        self.debug_logger = DebugLogger(config, self)

        # Set up logging
        logging.basicConfig(
            filename=os.path.join("Logs", datetime.now().strftime('%Y-%m-%d') + '.txt'),
            level=logging.INFO,
            format='%(asctime)s [%(levelname)s] %(message)s',
        )


    def print_all(self ,df, option = 1): 
        if option == 1:
            # Option 1: Temporarily show all rows 
            # with pd.option_context('display.max_rows', None, 'display.max_columns', None):
            with pd.option_context('display.max_rows', None):
                print(df)
            return
        # Option 2: Permanently change display settings (for current session) 
        pd.set_option('display.max_rows', None)
        # pd.set_option('display.max_columns', None)
        print(df)
    
    def get_symbol(self, symbol_var):
        return self.config.symbols[symbol_var.get()] + self.config.symbol_posfix.get()

    def onchange_symbol(self, symbol):
        symbol =  self.config.symbols[symbol] + self.config.symbol_posfix.get()
        point = mt5.symbol_info(symbol).point 
        self.set_status_label(f"⏳ Changed to {symbol} point {point} ({format(point, '.6f')})", "yellow")
        print(f"⏳ Changed to {symbol} point {point} ({format(point, '.6f')})")

    def get_data(self, symbol, timeframe, bars=100):
        rates = mt5.copy_rates_from_pos(symbol, timeframe, 0, bars)
        df = pd.DataFrame(rates)
        df['time'] = pd.to_datetime(df['time'], unit='s')
        return df

    def set_status_label(self, text, text_color = 'white'):
        self.config.status_label.configure(text=text, text_color=text_color)

    def add_status_frame(self, text, text_color = 'white'):
        status_text = f"{time.strftime('%H:%M:%S')} : {text}"

        # Thread-safe GUI update using after() method
        def update_gui():
            try:
                # Check if the parent frame still exists and is valid
                if not hasattr(self.config, 'status_scroll_frame') or not self.config.status_scroll_frame:
                    return

                # Try to access the frame to see if it's still valid
                try:
                    self.config.status_scroll_frame.winfo_exists()
                except:
                    return  # Frame no longer exists

                label = ctk.CTkLabel(self.config.status_scroll_frame, text=status_text, anchor="w", justify="left", text_color=text_color)

                # Safely add the label
                if self.config.status_scroll_labels:
                    # Check if the first label still exists
                    try:
                        if self.config.status_scroll_labels[0].winfo_exists():
                            label.pack(fill="x", anchor="w", before=self.config.status_scroll_labels[0])
                        else:
                            # Clean up invalid references
                            self.config.status_scroll_labels = [l for l in self.config.status_scroll_labels if l.winfo_exists()]
                            label.pack(fill="x", anchor="w")
                    except:
                        label.pack(fill="x", anchor="w")
                else:
                    label.pack(fill="x", anchor="w")

                self.config.status_scroll_labels.insert(0, label)

                # Safe cleanup of old labels
                while len(self.config.status_scroll_labels) > self.config.LIMIT_LOGS:
                    try:
                        old_label = self.config.status_scroll_labels.pop()
                        if old_label.winfo_exists():
                            old_label.destroy()
                    except:
                        # Label already destroyed, just remove from list
                        pass

            except Exception as e:
                print(f"GUI update error: {e}")

        # Schedule GUI update on main thread with additional safety
        if hasattr(self.config, 'status_scroll_frame') and self.config.status_scroll_frame:
            try:
                # Check if we can safely schedule the update
                self.config.status_scroll_frame.after_idle(update_gui)
            except:
                # If scheduling fails, just log to console
                print(status_text)

        logging.info(text)
        print(status_text)

    def safe_mt5_call(self, operation, *args, **kwargs):
        """Thread-safe wrapper for MT5 operations with timeout"""
        with self._mt5_lock:
            try:
                # Quick connection check
                if not mt5.initialize():
                    error_code = mt5.last_error()
                    self.add_status_frame(f"❌ MT5 not initialized: {error_code}", "red")
                    return None

                # Execute the operation
                result = operation(*args, **kwargs)

                # Check if operation failed and get error details
                if result is None and hasattr(mt5, 'last_error'):
                    error_code = mt5.last_error()
                    if error_code != (0, 'Success'):
                        self.add_status_frame(f"❌ MT5 operation failed: {error_code}", "yellow")

                return result
            except Exception as e:
                self.add_status_frame(f"❌ MT5 operation exception: {e}", "red")
                return None

    def safe_order_send(self, request):
        """Thread-safe wrapper specifically for mt5.order_send"""
        with self._mt5_lock:
            try:
                # Quick connection check
                if not mt5.initialize():
                    error_code = mt5.last_error()
                    self.add_status_frame(f"❌ MT5 not initialized: {error_code}", "red")
                    return None

                # Send the order
                result = mt5.order_send(request)

                # Check if operation failed and get error details
                if result is None:
                    error_code = mt5.last_error()
                    self.add_status_frame(f"❌ Order send failed: {error_code}", "red")

                return result
            except Exception as e:
                self.add_status_frame(f"❌ Order send exception: {e}", "red")
                return None

    def cleanup_memory(self):
        """Perform memory cleanup to prevent memory leaks"""
        try:
            # Force garbage collection
            gc.collect()

            # Clean up old status labels if too many exist
            if hasattr(self.config, 'status_scroll_labels') and len(self.config.status_scroll_labels) > self.config.LIMIT_LOGS * 0.8:
                excess_count = len(self.config.status_scroll_labels) - int(self.config.LIMIT_LOGS * 0.7)
                removed_count = 0

                # Safely remove labels from the end of the list
                for _ in range(excess_count):
                    if self.config.status_scroll_labels:
                        try:
                            old_label = self.config.status_scroll_labels.pop()
                            # Check if label still exists before destroying
                            if hasattr(old_label, 'winfo_exists') and old_label.winfo_exists():
                                old_label.destroy()
                            removed_count += 1
                        except Exception as e:
                            # Label already destroyed or invalid, just continue
                            print(f"Label cleanup error: {e}")
                            continue

                if removed_count > 0:
                    # Use print instead of add_status_frame to avoid recursion
                    print(f"🧹 Memory cleanup: Removed {removed_count} old status labels")

        except Exception as e:
            print(f"Memory cleanup error: {e}")

    def is_in_restricted_time(self):
        now_utc = datetime.utcnow().time()
        return dtime(21, 0) <= now_utc < dtime(22, 0)
    
    def get_limited_positions(self, symbol, filter_comment = "", limit=10):
        positions = mt5.positions_get(symbol=symbol)
        if positions is None:
            return []
        # กรองเฉพาะ TF ที่ต้องการ
        filtered = [pos for pos in positions if pos.comment.startswith(filter_comment)]
        # คืนกลับเฉพาะจำนวนที่ต้องการ
        return filtered[:limit]
    
    def get_volatility(self, symbol, timeframe=mt5.TIMEFRAME_M15, period=14):
        rates = mt5.copy_rates_from_pos(symbol, timeframe, 0, period + 1)
        if rates is None or len(rates) < period + 1:
            return None
        
        df = pd.DataFrame(rates)
        atr = ta.volatility.AverageTrueRange(high=df['high'], low=df['low'], close=df['close'], window=period)
        atr_value = atr.average_true_range().iloc[-1]
        return atr_value
    
    # 📤 ฟังก์ชันส่งข้อความไป LINE
    def send_line_message(self, message):
        if not self.LINE_TOKEN or self.LINE_TOKEN == '':
            return
        url = 'https://notify-api.line.me/api/notify'
        headers = {'Authorization': f'Bearer {self.config.LINE_TOKEN}'}
        data = {'message': message}
        requests.post(url, headers=headers, data=data)

    def send_telegram_message(bot_token, chat_id, message):
        # สร้าง Bot: เปิดกล่องค้นหาใน Telegram พิมพ์ @BotFather 
        # กด Start พิมพ์ /newbot → ตั้งชื่อ + username → ได้ "Bot Token"
        # เปิดแชทกับบอทของคุณ แล้วส่งข้อความใด ๆ
        # หาค่า chat_id จาก API
        url = f"https://api.telegram.org/bot{bot_token}/sendMessage"
        payload = {
            'chat_id': chat_id,
            'text': message,
            'parse_mode': 'HTML'
        }
        requests.post(url, data=payload)
        
    def send_discord_message(webhook_url, message):
        # ไปที่ Discord → สร้าง/เลือกห้อง → ตั้งค่า → "Integrations" → "Webhooks"
        # สร้าง Webhook และก๊อป URL มาใช้
        data = {
            "content": message
        }
        requests.post(webhook_url, json=data)

    def send_order(self, order_type, symbol, lot, entry, sl, tp, comment="Script"):
        try:
            self.add_status_frame(f"🔄 Preparing order: {order_type} {symbol} {lot} lots", "cyan")

            # Get price safely
            tick_info = self.safe_mt5_call(mt5.symbol_info_tick, symbol)
            if not tick_info:
                self.add_status_frame(f"❌ Failed to get price for {symbol}", "red")
                return None

            price = tick_info.bid if order_type == "Buy Now" else (tick_info.ask if order_type == "Sell Now" else entry)
            self.add_status_frame(f"📊 Price info: bid={tick_info.bid}, ask={tick_info.ask}, using={price}", "cyan")

            # Check if mappings exist
            if order_type not in self.config.action_type_mapping:
                self.add_status_frame(f"❌ Unknown order type: {order_type}", "red")
                return None

            if order_type not in self.config.order_type_mapping:
                self.add_status_frame(f"❌ Unknown order type mapping: {order_type}", "red")
                return None

            # Get symbol info safely
            symbol_info = self.safe_mt5_call(mt5.symbol_info, symbol)
            if not symbol_info:
                self.add_status_frame(f"❌ Failed to get point for {symbol}", "red")
                return None

            point = symbol_info.point  # e.g., 0.01 for XAUUSD
            original_sl_points = int(abs(float(entry) -  float(sl)) / point)  # e.g., 100 points

            request = {
                "action": self.config.action_type_mapping[order_type],
                "symbol": symbol,
                "volume": float(lot),
                "type": self.config.order_type_mapping[order_type],
                "price": float(price),
                "sl": float(sl),
                "tp": float(tp),
                "deviation": 10,
                "magic": original_sl_points, #161032,  # Unique identifier for your EA or script
                "comment": comment,
                "type_filling": mt5.ORDER_FILLING_FOK,
            }

            self.add_status_frame(f"📋 Order request prepared: {request}", "cyan")

            # Send the order using the safe wrapper
            self.add_status_frame(f"📤 Sending order request...", "cyan")
            result = self.safe_order_send(request)
            self.add_status_frame(f"📤 Order send result: {result}", "cyan")

        except Exception as e:
            self.add_status_frame(f"❌ Order preparation failed: {e}", "red")
            return None

        if not result:
            self.add_status_frame(f"❌ Execute failed: No return value", "yellow")
            return None
        elif result.retcode != mt5.TRADE_RETCODE_DONE:
            self.add_status_frame(f"❌ Execute failed: {result.comment}", "yellow")
            return result  # Return result even if failed, so caller can check retcode
        else:
            if order_type in ["Buy Limit", "Buy Now", "Buy Stop"]:
                self.add_status_frame(f"🟢 Sending {order_type}: {symbol} lot {lot} @ {entry}, SL {sl}, TP {tp} - {comment}", "lime")
            elif order_type in ["Sell Limit", "Sell Now", "Sell Stop"]:
                self.add_status_frame(f"🔴 Sending {order_type}: {symbol} lot {lot} @ {entry}, SL {sl}, TP {tp} - {comment}", "red")
            else:
                self.add_status_frame(f"⚠️ Execute failed: Order type unknown - {comment}", "yellow")

            return result  # Return successful result


    def positions_count(self, symbol, filter_comment = ""):
        orders = mt5.positions_get(symbol=symbol) if mt5.initialize() else [] 
        count_orders = {"All":0, "All_FT":0, "All_TF":0}
        for tf_name in self.config.timeframes:
            count_orders[tf_name] = {"B":0, "S":0}

        for order in orders:
            if order.symbol != symbol:
                continue

            count_orders["All"] += 1
            if order.comment.startswith(filter_comment) or filter_comment == "":
                count_orders["All_FT"] += 1
                for tf_name in self.config.timeframes:
                    if order.comment.startswith(filter_comment + tf_name):
                        count_orders[tf_name]["S" if order.type else "B"] += 1
                        # count_orders["All_TF"] += 1 

        return count_orders
    
    def update_SL_to_BE_by_point(self, symbol, filter_comment = "", is_moving_tp = False):
        """ Auto update orders STOP LOSS to BREAKEVEN """
        try:
            # Get positions safely with timeout
            orders = self.safe_mt5_call(mt5.positions_get, symbol=symbol)
            if not orders:
                return {"All":0, "All_FT":0, "All_TF":0}

            count_orders = {"All":0, "All_FT":0, "All_TF":0}
            for tf_name in self.config.timeframes:
                count_orders[tf_name] = {"B":0, "S":0}

            for order in orders:
                if order.symbol != symbol:
                    continue

                count_orders["All"] += 1
                if order.comment.startswith(filter_comment) or filter_comment == "":
                    count_orders["All_FT"] += 1
                    for tf_name in self.config.timeframes:
                        if order.comment.startswith(filter_comment + tf_name):
                            count_orders[tf_name]["S" if order.type else "B"] += 1
                            # count_orders["All_TF"] += 1
                    entry_price = order.price_open
                    current_price = order.price_current
                    current_tp = order.tp
                    current_sl = order.sl
                    type_ = order.type  # 0 = BUY, 1 = SELL
                    magic_number = order.magic

                    # Get symbol info safely
                    symbol_info = self.safe_mt5_call(mt5.symbol_info, order.symbol)
                    if not symbol_info:
                        continue

                    # Get original SL distance from magic number (stored as points)
                    if magic_number > 0 and magic_number != 155214:  # 155214 is default EA magic
                        # Magic number contains original SL distance in points
                        original_sl_points = magic_number
                        point_sl = original_sl_points * symbol_info.point

                        self.debug_logger.log_magic_number_info(
                            order.ticket, magic_number, original_sl_points, point_sl, order.symbol
                        )
                    else:
                        # Fallback to current calculation for orders without stored distance
                        point_sl = abs(entry_price - current_sl)
                        self.debug_logger.debug_print(f"#{order.ticket}: Using fallback SL distance calculation: {point_sl:.5f}")

                    factor = 2  # เริ่มจากคูณ 2
                    point = symbol_info.point
                    point_10 = point * 10
                    point_50 = point * 50
                    point_be = point_sl * factor

                    # point_sl = point * self.config.SL_POINTS 
                    # point_be = point * self.config.BE_POINTS 
                    new_sl = None
                    new_tp = current_tp

                    if type_ == mt5.ORDER_TYPE_BUY:  #and entry_price > current_sl: 
                        if is_moving_tp:
                            while current_price >= entry_price + (factor * point_sl):
                                new_sl = entry_price + ((factor - 1) * point_sl + point_10) 
                                new_tp = entry_price + ((factor + 1) * point_sl + point_50) 
                                factor += 1  
                        elif current_price >= entry_price + point_be:
                                new_sl = entry_price + point_sl
                                # new_sl = entry_price + point_10 
 
                    elif type_ == mt5.ORDER_TYPE_SELL: # and entry_price < current_sl:
                        if is_moving_tp:
                            while current_price <= entry_price - (factor * point_sl):
                                new_sl = entry_price - ((factor - 1) * point_sl + point_10) 
                                new_tp = entry_price - ((factor + 1) * point_sl + point_50)
                                factor += 1  
                        elif current_price <= entry_price - point_be:
                                new_sl = entry_price - point_sl
                                # new_sl = entry_price - point_10

                    # Execute the order modification if needed
                    if new_sl and abs(new_sl - current_sl) > symbol_info.point:  # Only if significant change
                        request = {"action": mt5.TRADE_ACTION_SLTP, "position": order.ticket, "sl": new_sl, "tp": new_tp}

                        # Send order modification using safe wrapper
                        result = self.safe_order_send(request)
                        if result and result.retcode == mt5.TRADE_RETCODE_DONE:
                            # Log successful SL change
                            self.debug_logger.log_order_action(order.ticket, "SL to BE", current_sl, new_sl, "SL", True)

                            # Log TP change if it was also modified
                            if new_tp != current_tp:
                                self.debug_logger.log_order_action(order.ticket, "Moving TP", current_tp, new_tp, "TP", True)
                        else:
                            error_msg = result.comment if result else "Unknown error"
                            self.debug_logger.log_error(Exception(f"Order send failed: {error_msg}"), "order modification", order.ticket)

            # Log processing summary
            group_name = filter_comment if filter_comment else "All"
            action_type = "SL+TP" if is_moving_tp else "SL to BE"
            processed_count = sum(1 for order in orders if (order.comment.startswith(filter_comment) or filter_comment == ""))
            self.debug_logger.log_order_processing(symbol, filter_comment, count_orders["All_FT"], processed_count, action_type)

            return count_orders
        except Exception as e:
            self.debug_logger.log_error(e, "update_SL_to_BE_by_point")
            return {"All":0, "All_FT":0, "All_TF":0}

    def update_TP_to_BE_by_condition(self, symbol, condition):
        """ Auto update orders STOP LOSS to BREAKEVEN """
        orders = mt5.positions_get() if mt5.initialize() else []
        if orders is None or len(orders) == 0:
            self.add_status_frame(f"❌ Try to update TP to BE {condition}: No open positions.", "yellow")
            return 0
        count = 0
        for order in orders:
            if order.symbol != symbol:
                continue
            count+=1
            entry_price = order.price_open
            current_price = order.price_current
            current_tp = order.tp
            current_sl = order.sl
            type_ = order.type  # 0 = BUY, 1 = SELL
            profit = order.profit
            symbol_info = mt5.symbol_info(order.symbol)
            if not symbol_info:
                continue

            should_update = False
            # เงื่อนไขปิด order ตาม condition ที่รับมา
            if condition == 'all-buy' and type_ == 0 and profit < 0:
                should_update = True
            elif condition == 'all-sell' and type_ == 1 and profit < 0:
                should_update = True
            elif condition == 'all' and profit < 0:
                should_update = True

            if should_update:
                point = symbol_info.point
                point_10 = point * 10
                # point_sl = point * self.config.SL_POINTS 
                new_tp = None

                if type_ == mt5.ORDER_TYPE_BUY: 
                    new_tp = entry_price + point_10
    
                elif type_ == mt5.ORDER_TYPE_SELL:
                    new_tp = entry_price - point_10 

                if new_tp:
                    request = {"action": mt5.TRADE_ACTION_SLTP, "position": order.ticket, "sl": current_sl, "tp": new_tp} 
                    result = mt5.order_send(request)
                    if result.retcode != mt5.TRADE_RETCODE_DONE:
                        self.add_status_frame(f"❌ Execute failed: {result.comment}", "yellow") 
                    else:
                        self.add_status_frame(f"📦 Set TP to BE: position {order.ticket} TP {current_tp} >> {new_tp}")

        return count
    
    def update_SL_to_BE_by_condition(self, symbol, condition):
        """ Auto update orders STOP LOSS to BREAKEVEN """
        orders = mt5.positions_get() if mt5.initialize() else []
        if orders is None or len(orders) == 0:
            self.add_status_frame(f"❌ Try to update SL to BE {condition}: No open positions.", "yellow")
            return 0
        count = 0
        for order in orders:
            if order.symbol != symbol:
                continue
            count+=1
            entry_price = order.price_open
            current_price = order.price_current
            current_tp = order.tp
            current_sl = order.sl
            type_ = order.type  # 0 = BUY, 1 = SELL
            profit = order.profit
            symbol_info = mt5.symbol_info(order.symbol)
            if not symbol_info:
                continue

            should_update = False
            # เงื่อนไขปิด order ตาม condition ที่รับมา
            if condition == 'all-buy' and type_ == 0 and profit > 0:
                should_update = True
            elif condition == 'all-sell' and type_ == 1 and profit > 0:
                should_update = True
            elif condition == 'all' and profit > 0:
                should_update = True

            if should_update:
                point = symbol_info.point
                point_10 = point * 10
                # point_sl = point * self.config.SL_POINTS 
                new_sl = None

                if type_ == mt5.ORDER_TYPE_BUY: 
                    new_sl = entry_price + point_10
    
                elif type_ == mt5.ORDER_TYPE_SELL:
                    new_sl = entry_price - point_10 

                if new_sl:
                    request = {"action": mt5.TRADE_ACTION_SLTP, "position": order.ticket, "sl": new_sl, "tp": current_tp}
                    result = mt5.order_send(request)
                    if result.retcode != mt5.TRADE_RETCODE_DONE:
                        self.add_status_frame(f"❌ Execute failed: {result.comment}", "yellow") 
                    else:
                        self.add_status_frame(f"📦 Set SL to BE: position {order.ticket} SL {current_sl} >> {new_sl}")

        return count

    def close_orders_by_condition(self, symbol, condition, filter_comment = ""):
        orders = mt5.positions_get() if mt5.initialize() else []
        closed = 0
        if orders is None or len(orders) == 0:
            self.add_status_frame(f"❌ Try to close {condition}: No open positions.", "yellow")
            return closed
        for order in orders:
            if order.symbol != symbol:
                continue
            ticket = order.ticket
            symbol_ = order.symbol
            type_ = order.type  # 0 = BUY, 1 = SELL
            profit = order.profit
            volume = order.volume

            should_close = False

            # เงื่อนไขปิด order ตาม condition ที่รับมา
            if condition == 'buy-profit' and type_ == 0 and profit > 0:
                should_close = True
            elif condition == 'sell-profit' and type_ == 1 and profit > 0:
                should_close = True
            elif condition == 'all-profit' and profit > 0:
                should_close = True
            elif condition == 'buy-loss' and type_ == 0 and profit < 0:
                should_close = True
            elif condition == 'sell-loss' and type_ == 1 and profit < 0:
                should_close = True
            elif condition == 'all-loss' and profit < 0:
                should_close = True
            elif condition == 'all-buy' and type_ == 0:
                should_close = True
            elif condition == 'all-sell' and type_ == 1:
                should_close = True
            elif condition == 'all':
                should_close = True
            elif condition == 'filter' and order.comment.startswith(filter_comment) or filter_comment == "":
                should_close = True

            if should_close:
                closed +=1
                price = mt5.symbol_info_tick(symbol_).bid if type_ == 0 else mt5.symbol_info_tick(symbol_).ask
                order_type = mt5.ORDER_TYPE_SELL if type_ == 0 else mt5.ORDER_TYPE_BUY
                close_request = {
                    "action": mt5.TRADE_ACTION_DEAL,
                    "position": ticket,
                    "symbol": symbol_,
                    "volume": volume,
                    "type": order_type,
                    "price": price,
                    "deviation": 10,
                    "magic": 155214,
                    "comment": "Auto close by condition",
                    "type_filling": mt5.ORDER_FILLING_FOK,
                }

                result = mt5.order_send(close_request)
                if result.retcode == mt5.TRADE_RETCODE_DONE:
                    self.add_status_frame(f"✅ Closed {symbol_} ({'BUY' if type_ == 0 else 'SELL'}) | profit: {profit:.2f}", "yellow")
                    # print(f"✅ Closed {symbol} ({'BUY' if type_ == 0 else 'SELL'}) | profit: {profit:.2f}")
                else:
                    self.add_status_frame(f"❌ Failed to close {symbol_}: {result.retcode}", "yellow")
                    # print(f"❌ Failed to close {symbol}: {result.retcode}")
        return closed

    def close_pending_orders_by_filter(self, symbol, filter_comment=""):
        """
        Close pending orders (Limit and Stop orders) that are not yet active
        If filter_comment is empty, closes all pending orders for the symbol
        If filter_comment is provided, only closes orders where comment ends with filter_comment
        """
        pending_orders = mt5.orders_get(symbol=symbol) if mt5.initialize() else []
        closed = 0

        if pending_orders is None or len(pending_orders) == 0:
            self.add_status_frame(f"❌ Try to close pending orders: No pending orders found for {symbol}.", "yellow")
            return closed

        for order in pending_orders:
            if order.symbol != symbol:
                continue

            # Check if we should close this order based on filter
            should_close = False
            if filter_comment == "":
                # Close all pending orders if no filter
                should_close = True
            else:
                # Close only if comment ends with filter_comment
                if order.comment.endswith(filter_comment):
                    should_close = True

            if should_close:
                # Cancel the pending order
                cancel_request = {
                    "action": mt5.TRADE_ACTION_REMOVE,
                    "order": order.ticket,
                }

                result = mt5.order_send(cancel_request)
                if result.retcode == mt5.TRADE_RETCODE_DONE:
                    closed += 1
                    order_type_name = "Buy Limit" if order.type == mt5.ORDER_TYPE_BUY_LIMIT else \
                                     "Sell Limit" if order.type == mt5.ORDER_TYPE_SELL_LIMIT else \
                                     "Buy Stop" if order.type == mt5.ORDER_TYPE_BUY_STOP else \
                                     "Sell Stop" if order.type == mt5.ORDER_TYPE_SELL_STOP else \
                                     f"Type {order.type}"
                    self.add_status_frame(f"✅ Cancelled pending order: {order.symbol} {order_type_name} | Comment: {order.comment}", "yellow")
                else:
                    self.add_status_frame(f"❌ Failed to cancel pending order {order.ticket}: {result.retcode}", "yellow")

        if closed > 0:
            filter_msg = f" with filter '{filter_comment}'" if filter_comment else ""
            self.add_status_frame(f"✅ Cancelled {closed} pending orders for {symbol}{filter_msg}", "green")

        return closed