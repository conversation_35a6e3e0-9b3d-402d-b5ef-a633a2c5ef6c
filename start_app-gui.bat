@echo off
REM MyApp Launcher - Double-click to start the application
REM This batch file activates the virtual environment and runs the app

echo Starting MyApp...
echo.

REM Change to the script directory
cd /d "%~dp0"

REM Check if virtual environment exists
if not exist "venv\Scripts\activate.bat" (
    echo ERROR: Virtual environment not found!
    echo Please make sure 'venv' folder exists in the same directory as this batch file.
    echo.
    pause
    exit /b 1
)

REM Activate virtual environment
echo Activating virtual environment...
call venv\Scripts\activate.bat

REM Check if main-gui.py exists
if not exist "main-gui.py" (
    echo ERROR: main-gui.py not found!
    echo Please make sure main-gui.py is in the same directory as this batch file.
    echo.
    pause
    exit /b 1
)

REM Run the application
echo Starting application...
echo.
pythonw main-gui.py

REM Keep window open if there's an error
if errorlevel 1 (
    echo.
    echo Application exited with an error.
    pause
)
