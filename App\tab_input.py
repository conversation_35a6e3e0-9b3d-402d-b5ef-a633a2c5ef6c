
import MetaTrader5 as mt5
import customtkinter as ctk
from tkinter import messagebox
import random
import string

# ===========================
# Class: TabInput
# ===========================
class TabInput:
    def __init__(self, master, config, util):
        self.frame = master.add("Input")
        self.config = config
        self.util = util

        self.symbol_var = ctk.StringVar(value="XU")
        self.lot_var = ctk.DoubleVar(value=0.02)
        self.order_type_var = ctk.StringVar()
        self.entry_var = ctk.DoubleVar()
        self.sl_var = ctk.DoubleVar()
        self.tp_lot_vars = []
        self.comment_var = ctk.StringVar(value="IN")

        self.form1 = ctk.CTkFrame(self.frame)
        self.form1.pack(pady=20)
        self.form2 = ctk.CTkFrame(self.frame)
        self.form2.pack(pady=20)

        self.build_form()

    def generate_input_id(self):
        """Generate random INPUT_ID with 8 characters (lowercase a-z + 0-9)"""
        characters = string.ascii_lowercase + string.digits
        return ''.join(random.choice(characters) for _ in range(8))

    def build_form(self):
        
        self.comment_var.set("IN")  # Default Comment
        self.comment_label = ctk.CTkLabel(self.form1, text="Comment")
        self.comment_label.grid(row=0, column=0, padx=10, pady=5)
        self.comment_val = ctk.CTkEntry(self.form1, textvariable=self.comment_var)
        self.comment_val.grid(row=0, column=1, columnspan=3, sticky='ew', padx=10, pady=5)

        ctk.CTkLabel(self.form1, text="Symbol:").grid(row=1, column=0, padx=10, pady=5)
        self.symbol_var.set("XU")  # Default symbol
        self.symbol_dropdown = ctk.CTkOptionMenu(self.form1, values=list(self.config.symbols), variable=self.symbol_var, command=self.util.onchange_symbol)
        self.symbol_dropdown.grid(row=1, column=1, padx=10, pady=5)

        ctk.CTkLabel(self.form1, text="Type:").grid(row=1, column=2, padx=10, pady=5)
        self.order_type_var.set("Buy Limit")  # Default order_type
        # self.order_type_dropdown = ctk.CTkOptionMenu(self.form1, values=["Buy Limit", "Sell Limit", "Buy Now", "Sell Now", "Buy Stop", "Sell Stop"], variable=self.order_type_var, command=self.on_order_type_change)
        self.order_type_dropdown = ctk.CTkOptionMenu(self.form1, values=["Buy Limit", "Sell Limit", "Buy Now", "Sell Now", "Buy Stop", "Sell Stop"], variable=self.order_type_var)
        self.order_type_dropdown.grid(row=1, column=3, padx=10, pady=5)

        ctk.CTkLabel(self.form1, text="Entry:").grid(row=2, column=0, padx=10, pady=5)
        self.order_entry = ctk.CTkEntry(self.form1, textvariable=self.entry_var)
        self.order_entry.grid(row=2, column=1, padx=10, pady=5)

        ctk.CTkLabel(self.form1, text="SL:").grid(row=2, column=2, padx=10, pady=5)
        self.order_sl = ctk.CTkEntry(self.form1, textvariable=self.sl_var)
        self.order_sl.grid(row=2, column=3, padx=10, pady=5)

        self.compute_Entry()
        self.add_tp_field()
        self.compute_SL_TP()

        ctk.CTkButton(self.frame, text="Add TP", command=self.add_tp_field, width=10).pack(side="left", padx=10)
        ctk.CTkButton(self.frame, text="Clear All", command=self.clear_all_tp_fields, width=10, fg_color="orange").pack(side="left", padx=5)
        ctk.CTkButton(self.frame, text="Get Entry", command=self.compute_Entry, width=10).pack(side="left", padx=10)
        ctk.CTkButton(self.frame, text="Compute", command=self.compute_SL_TP, width=10).pack(side="left", padx=10)
        ctk.CTkButton(self.frame, text="Submit", fg_color="green", command=self.submit_order).pack(side="right", padx=10)

    def add_tp_field(self): 
            
        if len(self.tp_lot_vars) < self.config.MAX_TP_FIELDS:
            tp_var = ctk.DoubleVar(value=0)
            lot_var = ctk.DoubleVar(value="0.01")
            irow = len(self.tp_lot_vars) + 2

            tp_label = ctk.CTkLabel(self.form2, text=f"TP{len(self.tp_lot_vars) + 1}:")
            tp_label.grid(row=irow, column=0)

            tp_entry = ctk.CTkEntry(self.form2, textvariable=tp_var)
            tp_entry.grid(row=irow, column=1, padx=10)

            lot_label = ctk.CTkLabel(self.form2, text="Lot:")
            lot_label.grid(row=irow, column=2)

            lot_entry = ctk.CTkEntry(self.form2, textvariable=lot_var)
            lot_entry.grid(row=irow, column=3, padx=10)

            delete_button = ctk.CTkButton(self.form2, text="❌", width=10, fg_color="red", command=lambda: self.del_tp_field(tp_label, tp_entry, lot_label, lot_entry, delete_button, tp_var, lot_var))
            delete_button.grid(row=irow, column=4)

            self.tp_lot_vars.append((tp_var, lot_var))
        else:
            self.status_label.configure(text=f"❌ Limit Reached: You can only add up to {self.config.MAX_TP_FIELDS} TP fields.")

    def del_tp_field(self, tp_label, tp_entry, lot_label, lot_entry, button, tp_var, lot_var):
        tp_label.destroy()
        tp_entry.destroy()
        lot_label.destroy()
        lot_entry.destroy()
        button.destroy()
        self.tp_lot_vars.remove((tp_var, lot_var))

    def clear_all_tp_fields(self):
        """Clear all TP fields and reset to default"""
        # Destroy all widgets in form2 (all TP-related widgets)
        for widget in self.form2.winfo_children():
            widget.destroy()

        # Clear the tp_lot_vars list
        self.tp_lot_vars.clear()

        # Add one default TP field
        self.add_tp_field()

        # Recompute SL and TP with default values
        self.compute_SL_TP()

        # Add status message
        if hasattr(self, 'util'):
            self.util.add_status_frame("🧹 All TP fields cleared and reset to default", "cyan")
 
    def submit_order(self):
        try:
            # Get user inputs
            symbol = self.util.get_symbol(self.symbol_var)
            entry = float(self.entry_var.get())
            sl = float(self.sl_var.get())
            order_type = self.order_type_var.get()

            # Collect TP and Lot values
            tp_lot_values = []
            confirmTxt = ""
            for tp_var, lot_var in self.tp_lot_vars:
                tp = float(tp_var.get())
                lot = float(lot_var.get())
                if tp and lot:
                    tp_lot_values.append((tp, lot))
                    tp_point = int(abs(entry - tp) * 100)
                    confirmTxt += f"\nTP {tp}  /  {tp_point} point"
    
            sl_point = int(abs(entry - sl) * 100)
            response=messagebox.askyesno("Order Confirmation!!",f'{order_type} \nET {entry} \nSL {sl}  /  {sl_point} point {confirmTxt}')
            if response:
                # Generate unique INPUT_ID for this order group
                input_id = self.generate_input_id()
                # base_comment = f"{self.comment_var.get()}_{input_id}"

                # Prepare and send orders with different take-profit levels and lot sizes
                i = 0
                successful_orders = 0
                failed_orders = 0

                for tp, lot in tp_lot_values:
                    i+=1
                    comment = self.comment_var.get() + f"_TP{i}_{input_id}"

                    # Send order and check result
                    result = self.util.send_order(order_type, symbol, lot, entry, sl, tp, comment)

                    if result is None:
                        self.util.add_status_frame(f"❌ Order {i} failed: No result returned", "red")
                        failed_orders += 1
                    elif hasattr(result, 'retcode') and result.retcode == mt5.TRADE_RETCODE_DONE:
                        self.util.add_status_frame(f"✅ Order {i} successful: {comment}", "green")
                        successful_orders += 1
                    else:
                        error_msg = result.comment if hasattr(result, 'comment') else "Unknown error"
                        self.util.add_status_frame(f"❌ Order {i} failed: {error_msg}", "red")
                        failed_orders += 1

                # Log the summary
                if successful_orders > 0:
                    self.util.add_status_frame(f"✅ {successful_orders} orders sent successfully with INPUT_ID: {input_id}", "green")
                if failed_orders > 0:
                    self.util.add_status_frame(f"❌ {failed_orders} orders failed", "red")
                # ctk.CTkMessageBox.show_info("Success", "Orders sent successfully!")
        except Exception as e:
            self.util.set_status_label(f"❌ Trade failed: {e}", "red")

    def compute_Entry(self):
        symbol = self.util.get_symbol(self.symbol_var) 
        order_type = self.order_type_var.get()
        tick = mt5.symbol_info_tick(symbol)
        # point = mt5.symbol_info(symbol).point
        if not tick:
            print(f"❌ No symbol: {symbol}")
            return
        if order_type in ["Buy Limit", "Buy Now", "Buy Stop"]:
            self.entry_var.set(tick.ask) 
        elif order_type in ["Sell Limit", "Sell Now", "Sell Stop"]:
            self.entry_var.set(tick.bid) 

    def compute_SL_TP(self):
        symbol = self.util.get_symbol(self.symbol_var) 
        order_type = self.order_type_var.get()
        point = mt5.symbol_info(symbol).point 
        entry = self.entry_var.get()
        if order_type in ["Buy Limit", "Buy Now", "Buy Stop"]:
            entry = mt5.symbol_info_tick(symbol).ask if order_type == "Buy Now" else entry
            self.sl_var.set(entry - (point * self.config.SL_POINTS))
            i = 0
            for tp_var, lot_var in self.tp_lot_vars: 
                i+=1
                tp_var.set(entry + (point * self.config.SL_POINTS * i)) 
        elif order_type in ["Sell Limit", "Sell Now", "Sell Stop"]:
            entry = mt5.symbol_info_tick(symbol).bid if order_type == "Sell Now" else entry
            self.sl_var.set(entry + (point * self.config.SL_POINTS))
            i = 0
            for tp_var, lot_var in self.tp_lot_vars: 
                i+=1
                tp_var.set(entry - (point * self.config.SL_POINTS * i)) 

            